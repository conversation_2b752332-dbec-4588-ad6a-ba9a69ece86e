# 新架构项目保存方案

## 概述

本文档详细描述了新架构的项目保存方案，旨在解决当前新架构在源文件保存方面的不足，确保项目素材的完整性和可移植性。本方案参考了旧架构的成熟实现，并结合新架构的特点进行了优化和改进。

## 当前问题分析

### 1. 源文件保存缺失

新架构目前只保存项目配置信息，没有实际保存源文件到项目目录：

```typescript
// 在 UnifiedProjectModule.ts 的 saveCurrentProject 方法中
const updatedProject: UnifiedProjectConfig = {
  // ... 其他配置
  
  // 注意：采用完全Meta驱动策略后，项目配置不再包含媒体相关字段
  // 所有媒体信息都通过扫描目录中的.meta文件获取
}
```

### 2. 媒体数据处理不当

虽然处理了时间轴项目中的媒体数据，但只是简单地去除了运行时对象，没有建立完整的保存机制：

```typescript
// mediaItems 包含 webav 运行时对象，需要清理
mediaItems: (mediaModule?.mediaItems.value || []).map(item => {
  // 创建媒体项目的可持久化副本，去掉运行时的 webav 对象
  const { webav, ...persistableItem } = item
  return persistableItem
}),
```

### 3. 缺少项目级媒体引用管理

新架构没有建立完整的项目级媒体引用管理机制，存在以下问题：
- 缺少项目级媒体引用管理器，无法统一管理项目内的媒体文件
- 缺少基于内容哈希的文件完整性验证机制
- 缺少媒体文件的生命周期管理，无法自动清理无效的媒体文件
- 媒体引用分散在项目配置和Meta文件中，缺少统一的查询和管理接口

## 解决方案设计

### 1. 整体架构设计

基于对现有代码的深入分析和最佳实践考虑，新架构的项目保存方案采用**即时保存 + Meta驱动加载 + 本地存储**的策略：

**核心理念**：
- **即时保存**：素材解析成功后立刻保存到项目本地磁盘，确保数据安全
- **Meta驱动架构**：通过"磁盘Meta文件为权威数据源 + 页面级内存缓存"的模式实现数据管理
  - **磁盘层**：Meta文件是唯一权威数据源，确保数据持久化和一致性
  - **内存层**：页面级媒体引用映射，提供快速查询和管理功能
  - **配置层**：项目配置文件不保存媒体引用信息，避免数据冗余
- **本地存储优先**：所有媒体文件都保存在各自项目的本地目录中，确保项目的完整性和可移植性

**页面级架构特性**：
- **页面级隔离**：通过href跳转重新加载页面，每个项目都是独立的页面实例
- **页面级管理器实例**：每个项目页面维护一个管理器实例
- **自动生命周期管理**：页面关闭时自动清理所有状态，下次加载从Meta文件重建

**分层设计**：
1. **媒体管理层**：负责媒体文件的即时保存、加载和验证
2. **项目配置层**：负责项目配置的保存和加载
3. **引用管理层**：负责页面级媒体引用映射的建立和维护
4. **元数据管理层**：负责媒体元数据的保存和加载，支持Meta驱动的加载策略
5. **✨ 页面级引用管理层**：负责当前项目页面内媒体引用的统一管理和完整性验证
6. **✨ 本地存储管理层**：负责媒体文件的本地存储和组织

### 2. 页面级媒体管理设计

**设计原则**：采用页面级媒体管理器设计。每个项目页面独立管理自己的媒体文件，通过页面跳转实现项目间完全隔离。

**实现策略**：
- **页面级实例化**：每个项目页面加载时创建媒体管理器实例
- **天然隔离性**：项目之间通过页面跳转完全隔离，无需复杂的状态管理
- **生命周期同步**：管理器生命周期与页面生命周期完全同步


### 3. 优化后的存储结构

```
workspace/
├── projects/
│   ├── project1/
│   │   ├── media/
│   │   │   ├── videos/           # 视频文件及其.meta文件
│   │   │   │   ├── video1.mp4
│   │   │   │   ├── video1.mp4.meta
│   │   │   │   └── video2.mov.meta
│   │   │   ├── images/           # 图片文件及其.meta文件
│   │   │   │   ├── image1.jpg
│   │   │   │   └── image1.jpg.meta
│   │   │   ├── audio/            # 音频文件及其.meta文件
│   │   │   └── thumbnails/       # 项目缩略图
│   │   └── project.json          # 项目配置（不包含媒体引用）
│   └── project2/
```

**核心特性**：
- **唯一数据源**：项目目录中的 `.meta` 文件是唯一权威数据源
- **Meta驱动加载**：完全通过扫描目录的meta文件来构建媒体引用
- **即时保存**：素材解析成功后立刻保存到项目本地磁盘
- **容错机制**：自动过滤无效的meta文件，避免加载不存在的媒体

### 4. 核心组件设计

#### 4.1 ProjectMediaManager 核心实现

```typescript
// frontend/src/unified/utils/ProjectMediaManager.ts

// Meta文件扫描过程中使用的临时数据结构
interface MetaFileInfo {
  metaFileName: string              // meta文件名
  sourceFileName: string            // 对应的源文件名
  relativePath: string              // 相对路径（不含.meta后缀）
  dirHandle: FileSystemDirectoryHandle  // 目录句柄
  metadata: UnifiedMediaMetadata    // 解析后的元数据
}

export class ProjectMediaManager {
  private projectId: string = ''
  private mediaReferences: Map<string, UnifiedMediaReference> = new Map()
  
  constructor() {
    // 无参构造函数，支持页面级全局实例化
  }
  
  /**
   * 初始化项目媒体管理器
   * @param projectId 项目ID
   */
  initializeForProject(projectId: string): void {
    this.projectId = projectId
    this.mediaReferences.clear()
    console.log(`🔧 初始化页面级媒体管理器: ${projectId}`)
  }
  
  /**
   * 保存媒体文件到项目本地目录
   * @param file 媒体文件
   * @param mediaType 媒体类型
   * @param clip WebAV Clip对象（可选，用于生成完整元数据）
   * @returns 媒体保存结果
   */
  async saveMediaToProject(
    file: File,
    mediaType: MediaType,
    clip?: any
  ): Promise<MediaSaveResult> {
    try {
      console.log(`💾 开始保存媒体文件到项目: ${file.name}`)
      
      // 1. 计算内容哈希用于去重
      const contentHash = await this.calculateChecksum(file)
      
      // 2. 检查是否已存在相同文件（通过遍历查找相同哈希）
      const existingRef = Array.from(this.mediaReferences.values()).find(
        ref => ref.metadata && ref.metadata.checksum === contentHash
      )
      if (existingRef) {
        console.log(`♻️ 复用现有媒体: ${file.name} -> ${existingRef.id}`)
        return {
          success: true,
          mediaReference: existingRef,
          storagePath: existingRef.storedPath,
          isReused: true
        }
      }
      
      // 3. 生成存储路径
      const storagePath = this.generateStoragePath(file.name, mediaType)
      
      // 4. 保存文件到项目目录
      await this.saveFileToStorage(file, storagePath)
      
      // 5. 生成持久化ID和媒体元数据
      const mediaId = generateUUID4()
      let metadata: UnifiedMediaMetadata
      
      if (clip) {
        // 如果有clip，生成完整的元数据（包含duration、width、height等）
        metadata = await this.generateMediaMetadata(file, clip, mediaType, mediaId, contentHash)
      } else {
        // 如果没有clip，只生成基础元数据
        metadata = {
          id: mediaId,
          originalFileName: file.name,
          fileSize: file.size,
          mimeType: file.type,
          checksum: contentHash,
          importedAt: new Date().toISOString(),
        }
      }
      
      // 6. 创建媒体引用
      const mediaReference: UnifiedMediaReference = {
        id: mediaId,                   // 使用相同的ID
        storedPath: storagePath,
        mediaType,
        metadata,
        status: 'ready'
      }
      
      // 7. 注册引用
      this.mediaReferences.set(mediaReference.id, mediaReference)
      
      // 8. 保存元数据到.meta文件
      await this.saveMediaMetadata(this.projectId, storagePath, metadata)
      
      console.log(`✅ 媒体文件保存成功: ${file.name} -> ${storagePath}`)
      
      return {
        success: true,
        mediaReference,
        storagePath,
        isReused: false
      }
    } catch (error) {
      console.error('保存媒体文件失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }
  
  /**
   * Meta驱动扫描项目媒体目录
   * @returns 媒体引用数组
   */
  async scanMediaDirectory(): Promise<UnifiedMediaReference[]> {
    try {
      console.log(`🔍 开始Meta驱动扫描项目媒体目录: ${this.projectId}`)
      
      // 1. 扫描项目目录中的所有meta文件
      const allMetaFiles = await this.scanAllMetaFiles()
      console.log(`📄 发现 ${allMetaFiles.length} 个meta文件`)
      
      // 2. 逐个验证对应的源文件是否存在，过滤掉不存在源文件的meta
      const validReferences: UnifiedMediaReference[] = []
      
      for (const metaInfo of allMetaFiles) {
        try {
          // 验证对应的源文件是否存在
          const sourceFileExists = await this.verifySourceFileExists(metaInfo.dirHandle, metaInfo.sourceFileName)
          
          if (sourceFileExists) {
            // 源文件存在，创建有效的媒体引用
            const mediaType = this.inferMediaTypeFromPath(metaInfo.relativePath)
            
            const reference: UnifiedMediaReference = {
              id: metaInfo.metadata.id, // 从meta文件读取持久化ID
              storedPath: `media/${metaInfo.relativePath}`,
              mediaType,
              metadata: metaInfo.metadata,
              status: 'ready'
            }
            
            // 更新内存中的引用映射
            this.mediaReferences.set(reference.id, reference)
            
            validReferences.push(reference)
            console.log(`✅ 验证通过: ${metaInfo.metadata.originalFileName}`)
          } else {
            console.warn(`⚠️ 源文件不存在，跳过meta文件: ${metaInfo.sourceFileName}`)
          }
        } catch (error) {
          console.error(`验证meta文件失败: ${metaInfo.metaFileName}`, error)
        }
      }
      
      console.log(`📁 Meta驱动扫描完成，有效媒体引用: ${validReferences.length}/${allMetaFiles.length}`)
      return validReferences
    } catch (error) {
      console.error('Meta驱动扫描媒体目录失败:', error)
      return [] // 返回空数组而不是抛出错误，保证加载流程的健壮性
    }
  }
  
  
  /**
   * 扫描项目目录中的所有meta文件
   * @returns meta文件信息数组
   */
  private async scanAllMetaFiles(): Promise<MetaFileInfo[]> {
    try {
      const workspaceHandle = await directoryManager.getWorkspaceHandle()
      if (!workspaceHandle) return []
      
      const projectsHandle = await workspaceHandle.getDirectoryHandle('projects')
      const projectHandle = await projectsHandle.getDirectoryHandle(this.projectId)
      const mediaHandle = await projectHandle.getDirectoryHandle('media')
      
      const metaFiles: MetaFileInfo[] = []
      await this.scanDirectoryForMeta(mediaHandle, '', metaFiles)
      
      return metaFiles
    } catch (error) {
      console.error('扫描meta文件失败:', error)
      return []
    }
  }
  
  /**
   * 递归扫描目录中的meta文件
   */
  private async scanDirectoryForMeta(
    dirHandle: FileSystemDirectoryHandle,
    relativePath: string,
    metaFiles: MetaFileInfo[]
  ): Promise<void> {
    try {
      for await (const [name, handle] of dirHandle.entries()) {
        const currentPath = relativePath ? `${relativePath}/${name}` : name
        
        if (handle.kind === 'directory') {
          await this.scanDirectoryForMeta(handle, currentPath, metaFiles)
        } else if (handle.kind === 'file' && name.endsWith('.meta')) {
          try {
            const file = await handle.getFile()
            const content = await file.text()
            const metadata = JSON.parse(content) as UnifiedMediaMetadata
            
            const sourceFileName = name.replace('.meta', '')
            const sourceRelativePath = currentPath.replace('.meta', '')
            
            metaFiles.push({
              metaFileName: name,
              sourceFileName,
              relativePath: sourceRelativePath,
              dirHandle,
              metadata
            })
          } catch (error) {
            console.warn(`解析meta文件失败: ${currentPath}`, error)
          }
        }
      }
    } catch (error) {
      console.error(`扫描目录失败: ${relativePath}`, error)
    }
  }
  
  /**
   * 验证源文件是否存在
   */
  private async verifySourceFileExists(
    dirHandle: FileSystemDirectoryHandle,
    sourceFileName: string
  ): Promise<boolean> {
    try {
      await dirHandle.getFileHandle(sourceFileName)
      return true
    } catch (error) {
      return false
    }
  }
  
  /**
   * 从路径推断媒体类型
   */
  private inferMediaTypeFromPath(relativePath: string): MediaType {
    const fileName = relativePath.toLowerCase()
    
    if (fileName.match(/\.(mp4|avi|mov|webm|mkv)$/)) return 'video'
    if (fileName.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/)) return 'image'
    if (fileName.match(/\.(mp3|wav|ogg|aac|flac)$/)) return 'audio'
    if (fileName.match(/\.(txt|md|json|xml)$/)) return 'text'
    
    return 'video' // 默认类型
  }
  
  /**
   * 生成存储路径
   */
  private generateStoragePath(fileName: string, mediaType: MediaType): string {
    const timestamp = Date.now()
    const extension = this.getFileExtension(fileName)
    const sanitizedName = fileName.replace(extension, '').replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_')
    const uniqueName = `${sanitizedName}_${timestamp}${extension}`
    
    return `media/${mediaType}s/${uniqueName}`
  }
  
  /**
   * 保存文件到存储位置
   */
  private async saveFileToStorage(file: File, storagePath: string): Promise<void> {
    try {
      const workspaceHandle = await directoryManager.getWorkspaceHandle()
      if (!workspaceHandle) throw new Error('未设置工作目录')
      
      const projectsHandle = await workspaceHandle.getDirectoryHandle('projects')
      const projectHandle = await projectsHandle.getDirectoryHandle(this.projectId)
      
      // 解析路径并确保目录存在
      const pathParts = storagePath.split('/')
      const fileName = pathParts.pop()!
      
      let currentHandle = projectHandle
      for (const part of pathParts) {
        if (part) {
          currentHandle = await this.ensureDirectoryExists(currentHandle, part)
        }
      }
      
      // 保存文件
      const fileHandle = await currentHandle.getFileHandle(fileName, { create: true })
      const writable = await fileHandle.createWritable()
      await writable.write(file)
      await writable.close()
    } catch (error) {
      console.error('保存文件到存储失败:', error)
      throw new Error(`保存文件失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  /**
   * 验证媒体文件是否存在
   */
  private async verifyMediaFileExists(storagePath: string): Promise<boolean> {
    try {
      const workspaceHandle = await directoryManager.getWorkspaceHandle()
      if (!workspaceHandle) return false
      
      const pathParts = storagePath.split('/')
      const fileName = pathParts.pop()!
      
      let currentHandle = workspaceHandle
      for (const part of pathParts) {
        if (part) {
          currentHandle = await currentHandle.getDirectoryHandle(part)
        }
      }
      
      await currentHandle.getFileHandle(fileName)
      return true
    } catch (error) {
      return false
    }
  }
  
  /**
   * 从元数据推断媒体类型
   */
  private inferMediaTypeFromMetadata(metadata: UnifiedMediaMetadata): MediaType {
    const mimeType = metadata.mimeType.toLowerCase()
    
    if (mimeType.startsWith('video/')) return 'video'
    if (mimeType.startsWith('image/')) return 'image'
    if (mimeType.startsWith('audio/')) return 'audio'
    if (mimeType.startsWith('text/')) return 'text'
    
    // 根据文件扩展名推断
    const fileName = metadata.originalFileName.toLowerCase()
    if (fileName.match(/\.(mp4|avi|mov|webm|mkv)$/)) return 'video'
    if (fileName.match(/\.(jpg|jpeg|png|gif|webp|bmp)$/)) return 'image'
    if (fileName.match(/\.(mp3|wav|ogg|aac|flac)$/)) return 'audio'
    if (fileName.match(/\.(txt|md|json|xml)$/)) return 'text'
    
    return 'video' // 默认类型
  }
  
  /**
   * 生成媒体元数据
   * @param file 媒体文件
   * @param clip WebAV Clip对象
   * @param mediaType 媒体类型
   * @param mediaId 媒体ID
   * @param checksum 文件校验和
   * @returns 媒体元数据
   */
  async generateMediaMetadata(
    file: File,
    clip: any,
    mediaType: MediaType,
    mediaId: string,
    checksum: string,
  ): Promise<UnifiedMediaMetadata> {
    try {
      // 等待clip准备完成
      const meta = await clip.ready
      
      // 构建元数据
      const metadata: UnifiedMediaMetadata = {
        id: mediaId,                  // 持久化ID
        originalFileName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        checksum: checksum,           // 文件校验和
        importedAt: new Date().toISOString(),
      }
      
      // 根据媒体类型添加特定元数据
      if (mediaType === 'video' || mediaType === 'audio') {
        metadata.duration = meta.duration
      }
      
      if (mediaType === 'video' || mediaType === 'image') {
        metadata.width = meta.width
        metadata.height = meta.height
      }
      
      return metadata
    } catch (error) {
      console.error('生成媒体元数据失败:', error)
      throw new Error(`生成媒体元数据失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  /**
   * 保存媒体元数据文件
   * @param projectId 项目ID
   * @param storedPath 存储路径
   * @param metadata 媒体元数据
   */
  async saveMediaMetadata(
    projectId: string,
    storedPath: string,
    metadata: UnifiedMediaMetadata,
  ): Promise<void> {
    try {
      const workspaceHandle = await directoryManager.getWorkspaceHandle()
      if (!workspaceHandle) throw new Error('未设置工作目录')
      
      const projectsHandle = await workspaceHandle.getDirectoryHandle('projects')
      const projectHandle = await projectsHandle.getDirectoryHandle(projectId)
      
      // 构建元数据文件路径
      const metaFilePath = `${storedPath}.meta`
      const pathParts = metaFilePath.split('/')
      const fileName = pathParts.pop()!
      const dirPath = pathParts.join('/')
      
      // 确保目录存在
      let currentHandle = projectHandle
      for (const part of dirPath.split('/')) {
        if (part) {
          currentHandle = await this.ensureDirectoryExists(currentHandle, part)
        }
      }
      
      // 保存元数据文件
      const fileHandle = await currentHandle.getFileHandle(fileName, { create: true })
      const writable = await fileHandle.createWritable()
      await writable.write(JSON.stringify(metadata, null, 2))
      await writable.close()
      
      console.log(`💾 元数据文件保存成功: ${metaFilePath}`)
    } catch (error) {
      console.error('保存媒体元数据失败:', error)
      throw new Error(`保存媒体元数据失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  // ... 其他现有方法保持不变 ...
  
  /**
   * 计算文件校验和
   * @param file 媒体文件
   * @returns 校验和字符串
   */
  async calculateChecksum(file: File): Promise<string> {
    try {
      const buffer = await file.arrayBuffer()
      const hashBuffer = await crypto.subtle.digest('SHA-256', buffer)
      const hashArray = Array.from(new Uint8Array(hashBuffer))
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
    } catch (error) {
      console.error('计算文件校验和失败:', error)
      throw new Error(`计算文件校验和失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  /**
   * 验证文件完整性
   * @param projectId 项目ID
   * @param storedPath 存储路径
   * @param expectedChecksum 预期校验和
   * @returns 是否验证通过
   */
  async verifyMediaIntegrity(
    projectId: string,
    storedPath: string,
    expectedChecksum: string,
  ): Promise<boolean> {
    try {
      const file = await this.loadMediaFromProject(projectId, storedPath)
      const actualChecksum = await this.calculateChecksum(file)
      return actualChecksum === expectedChecksum
    } catch (error) {
      console.error('验证媒体文件完整性失败:', error)
      return false
    }
  }
  
  /**
   * 从项目目录加载媒体文件
   * @param projectId 项目ID
   * @param storedPath 存储路径
   * @returns 媒体文件
   */
  async loadMediaFromProject(projectId: string, storedPath: string): Promise<File> {
    try {
      const workspaceHandle = await directoryManager.getWorkspaceHandle()
      if (!workspaceHandle) throw new Error('未设置工作目录')
      
      const projectsHandle = await workspaceHandle.getDirectoryHandle('projects')
      const projectHandle = await projectsHandle.getDirectoryHandle(projectId)
      
      // 解析路径并获取文件
      const pathParts = storedPath.split('/')
      let currentHandle: FileSystemDirectoryHandle = projectHandle
      
      // 遍历目录路径
      for (let i = 0; i < pathParts.length - 1; i++) {
        const part = pathParts[i]
        if (part) {
          currentHandle = await currentHandle.getDirectoryHandle(part)
        }
      }
      
      // 获取文件
      const fileName = pathParts[pathParts.length - 1]
      const fileHandle = await currentHandle.getFileHandle(fileName)
      const file = await fileHandle.getFile()
      
      return file
    } catch (error) {
      console.error('从项目加载媒体文件失败:', error)
      throw new Error(`加载媒体文件失败: ${error instanceof Error ? error.message : String(error)}`)
    }
  }
  
  /**
   * 确保目录存在
   * @param parentHandle 父目录句柄
   * @param dirName 目录名
   * @returns 目录句柄
   */
  private async ensureDirectoryExists(parentHandle: FileSystemDirectoryHandle, dirName: string): Promise<FileSystemDirectoryHandle> {
    try {
      return await parentHandle.getDirectoryHandle(dirName)
    } catch (error) {
      if ((error as DOMException).name === 'NotFoundError') {
        return await parentHandle.getDirectoryHandle(dirName, { create: true })
      }
      throw error
    }
  }
  
  /**
   * 获取文件扩展名
   * @param fileName 文件名
   * @returns 文件扩展名
   */
  private getFileExtension(fileName: string): string {
    const lastDotIndex = fileName.lastIndexOf('.')
    return lastDotIndex !== -1 ? fileName.substring(lastDotIndex) : ''
  }
}

// ==================== 页面级实例 ====================

// 导出页面级实例，每个项目页面维护一个独立的管理器实例
export const globalProjectMediaManager = new ProjectMediaManager()

// ==================== 便捷函数 ====================

/**
 * 初始化页面级项目媒体管理器
 * 在项目页面加载时调用，设置当前管理器服务的项目ID
 */
export function initializeProjectMediaManager(projectId: string): void {
  globalProjectMediaManager.initializeForProject(projectId)
}

/**
 * 保存媒体文件到当前项目页面
 */
export async function saveMediaToCurrentProject(
  file: File,
  mediaType: MediaType
): Promise<MediaSaveResult> {
  return await globalProjectMediaManager.saveMediaToProject(file, mediaType)
}

/**
 * 扫描当前项目页面的媒体目录
 */
export async function scanCurrentProjectMedia(): Promise<UnifiedMediaReference[]> {
  return await globalProjectMediaManager.scanMediaDirectory()
}
```

#### 2.2 项目配置结构

`UnifiedProjectConfig` 采用Meta驱动策略，项目配置不包含媒体引用字段：

```typescript
// frontend/src/unified/project/types.ts
export interface UnifiedProjectConfig {
  id: string
  name: string
  description: string
  createdAt: string
  updatedAt: string
  version: string
  thumbnail?: string
  duration: number // 项目总时长（秒）

  // 项目设置
  settings: {
    videoResolution: {
      name: string
      width: number
      height: number
      aspectRatio: string
    }
    frameRate: number // 固定30帧
    timelineDurationFrames: number
  }

  // 时间轴数据（使用统一类型）
  timeline: {
    tracks: UnifiedTrackData[]
    timelineItems: UnifiedTimelineItemData[]
    mediaItems: UnifiedMediaItemData[]
  }
}

// 注意：以下接口仅用于页面级内存缓存，用于快速查询和管理，不保存到项目配置文件中

export interface UnifiedMediaReference {
  id: string                   // 从metadata.id获取，保持持久化一致性
  storedPath: string           // 存储路径
  mediaType: MediaType         // 媒体类型
  metadata: UnifiedMediaMetadata // 媒体元数据
  status?: 'ready' | 'error'   // 状态
}

export interface UnifiedMediaMetadata {
  id: string                   // UUID4，持久化标识（保存到meta文件中）
  originalFileName: string      // 原始文件名
  fileSize: number             // 文件大小
  mimeType: string             // MIME类型
  checksum: string             // 文件校验和（用于去重）
  importedAt: string           // 导入时间
  duration?: number            // 持续时间（视频/音频）
  width?: number               // 宽度（视频/图片）
  height?: number              // 高度（视频/图片）
}

export type MediaType = 'video' | 'image' | 'audio' | 'text'

```

### 3. 优化后的项目保存流程设计

#### 3.1 即时保存策略

**核心思想**：在素材解析成功后立刻保存到本地磁盘，而不是等到项目保存时才处理。

**实现位置**：修改 `UnifiedMediaModule.ts` 中的 `startWebAVProcessing` 方法：

```typescript
// frontend/src/unified/modules/UnifiedMediaModule.ts
import { globalProjectMediaManager } from '@/unified/utils/projectMediaManager'

async function startWebAVProcessing(mediaItem: UnifiedMediaItemData): Promise<void> {
  console.log(`🎬 [UnifiedMediaModule] 开始WebAV解析: ${mediaItem.name}`)

  try {
    // ... 现有的WebAV解析逻辑 ...
    
    // 等待clip准备完成
    const meta = await clip.ready
    
    // ✨ 新增：解析成功后立即保存到磁盘，并在数据源中设置引用ID
    if (mediaItem.source.file) {
      try {
        // 保存媒体文件和元数据到当前项目页面
        const saveResult = await globalProjectMediaManager.saveMediaToProject(
          mediaItem.source.file,
          mediaItem.mediaType,
          clip  // 传入clip用于生成完整元数据
        )
        
        // 🆕 关键改进：在数据源中设置媒体管理器引用ID
        if (saveResult.success && saveResult.mediaReference) {
          mediaItem.source.mediaReferenceId = saveResult.mediaReference.id
        }
        
        console.log(`💾 媒体文件即时保存成功: ${mediaItem.name} -> ${saveResult.storagePath}`)
        console.log(`🔗 媒体引用ID已设置: ${mediaItem.source.mediaReferenceId}`)
      } catch (saveError) {
        console.error(`❌ 媒体文件即时保存失败: ${mediaItem.name}`, saveError)
        // 保存失败不影响WebAV解析流程，继续处理
        console.warn(`媒体文件保存失败，但WebAV解析继续: ${mediaItem.name}`, saveError)
      }
    }
    
    // ... 现有的WebAV对象设置和状态转换逻辑 ...
    
    // 转换到ready状态
    UnifiedMediaItemActions.transitionTo(mediaItem, 'ready')
    
    console.log(`✅ [UnifiedMediaModule] WebAV解析完成: ${mediaItem.name}`)
  } catch (error) {
    // ... 现有的错误处理逻辑 ...
  }
}
```

#### 3.2 简化的项目保存方法

由于媒体文件已经即时保存，项目保存时只需要保存项目配置：

```typescript
// frontend/src/unified/modules/UnifiedProjectModule.ts
async function saveCurrentProject(): Promise<void> {
  try {
    isSaving.value = true
    console.log(`💾 保存项目: ${configModule.projectName.value}`)
    configModule.projectUpdatedAt.value = new Date().toISOString()
    
    // 构建项目配置（采用完全Meta驱动策略，不保存媒体引用信息）
    const updatedProject: UnifiedProjectConfig = {
      id: configModule.projectId.value,
      name: configModule.projectName.value,
      description: configModule.projectDescription.value,
      createdAt: configModule.projectCreatedAt.value,
      updatedAt: configModule.projectUpdatedAt.value,
      version: configModule.projectVersion.value,
      thumbnail: configModule.projectThumbnail.value || undefined,
      duration: configModule.projectDuration.value,
      settings: {
        videoResolution: configModule.videoResolution.value,
        frameRate: configModule.frameRate.value,
        timelineDurationFrames: configModule.timelineDurationFrames.value,
      },
      timeline: {
        tracks: trackModule?.tracks.value || [],
        timelineItems: (timelineModule?.timelineItems.value || []).map(item => {
          const clonedItem = TimelineItemFactory.clone(item)
          if (clonedItem.runtime) {
            clonedItem.runtime = {}
          }
          return clonedItem
        }),
        mediaItems: (mediaModule?.mediaItems.value || []).map(item => {
          const { webav, ...persistableItem } = item
          return persistableItem
        }),
      }
    }
    
    // 保存项目配置（媒体文件已在即时保存中处理）
    await unifiedProjectManager.saveProject(updatedProject)
    
    console.log(`✅ 项目保存成功: ${configModule.projectName.value}`)
  } catch (error) {
    console.error('保存项目失败:', error)
    throw error
  } finally {
    isSaving.value = false
  }
}
```

#### 3.3 页面级项目加载方法

**页面级管理器初始化**：项目页面加载时初始化页面级 `ProjectMediaManager` 实例。

修改 `UnifiedProjectManager.ts` 中的 `loadProjectContent` 方法：

```typescript
// frontend/src/unified/utils/projectManager.ts
import { globalProjectMediaManager } from './projectMediaManager'

async loadProjectContent(
  projectId: string,
  options: UnifiedLoadProjectOptions = {},
): Promise<UnifiedProjectLoadResult | null> {
  const { loadMedia = true, loadTimeline = true, onProgress } = options
  
  try {
    // 1. 加载项目配置
    onProgress?.('加载项目配置...', 10)
    const projectConfig = await this.loadProjectConfig(projectId)
    if (!projectConfig) {
      throw new Error('项目配置不存在')
    }
    
    // 2. 初始化页面级媒体管理器
    globalProjectMediaManager.initializeForProject(projectId)
    
    // 3. Meta驱动的媒体加载策略
    let mediaItems: UnifiedMediaItemData[] = []
    if (loadMedia) {
      onProgress?.('扫描媒体目录...', 20)
      
      // 通过扫描目录meta文件构建媒体引用（Meta驱动策略）
      const scannedReferences = await globalProjectMediaManager.scanMediaDirectory()
      
      onProgress?.('重建媒体项目...', 40)
      mediaItems = await this.rebuildMediaItemsFromReferences(
        scannedReferences,
        (loaded, total) => {
          const progress = 40 + (loaded / total) * 30
          onProgress?.(`重建媒体项目 ${loaded}/${total}`, progress)
        }
      )
      
      // 🆕 新增：差异化加载不同类型的数据源
      await this.loadDataSourcesWithDifferentStrategies(mediaItems, onProgress)
    }
    
    // 4. 加载时间轴数据
    let timelineItems: UnifiedTimelineItemData[] = []
    let tracks: UnifiedTrackData[] = []
    if (loadTimeline && projectConfig.timeline) {
      onProgress?.('加载时间轴数据...', 80)
      timelineItems = projectConfig.timeline.timelineItems || []
      tracks = projectConfig.timeline.tracks || []
    }
    
    // 5. 返回加载结果
    onProgress?.('完成加载', 100)
    return {
      projectConfig,
      mediaItems,
      timelineItems,
      tracks,
      // 无需返回管理器实例，页面级全局可访问
      loadedStages: ['config', 'media', 'timeline']
    }
  } catch (error) {
    console.error(`加载项目内容失败: ${projectId}`, error)
    throw error
  }
}
```

#### 3.4 差异化数据源加载策略

**核心设计思想**：根据不同的数据源类型(source.type)采用不同的加载策略，通过`mediaReferenceId`建立与媒体管理器的关联。

```typescript
/**
 * 差异化加载不同类型数据源的策略方法
 * 根据不同source类型实现不同的加载逻辑
 */
async loadDataSourcesWithDifferentStrategies(
  mediaItems: UnifiedMediaItemData[],
  onProgress?: (message: string, progress: number) => void
): Promise<void> {
  onProgress?.('差异化加载数据源...', 70)
  
  for (const mediaItem of mediaItems) {
    try {
      // 🔄 根据数据源类型采用不同的加载策略
      if (DataSourceQueries.isUserSelectedSource(mediaItem.source)) {
        // 👤 本地文件数据源：根据mediaReferenceId从媒体管理器加载文件
        await this.loadUserSelectedFileSource(mediaItem.source)
      } else if (DataSourceQueries.isRemoteSource(mediaItem.source)) {
        // 🌐 远程文件数据源：优先查找本地缓存，不存在时启动下载
        await this.loadRemoteFileSource(mediaItem.source)
      }
    } catch (error) {
      console.error(`加载数据源失败: ${mediaItem.name}`, error)
      // 单个文件加载失败不影响其他文件
      DataSourceBusinessActions.setError(mediaItem.source, `加载失败: ${error.message}`)
    }
  }
}

/**
 * 加载本地文件数据源
 * 策略：根据mediaReferenceId从媒体管理器获取对应的file
 */
async loadUserSelectedFileSource(source: UserSelectedFileSourceData): Promise<void> {
  if (!source.mediaReferenceId) {
    console.warn('本地文件数据源缺少mediaReferenceId，无法加载')
    DataSourceBusinessActions.setError(source, '缺少媒体引用ID')
    return
  }
  
  try {
    // 根据引用ID从媒体管理器获取媒体引用
    const mediaRef = await globalProjectMediaManager.getMediaReference(source.mediaReferenceId)
    if (!mediaRef) {
      console.warn(`媒体引用不存在: ${source.mediaReferenceId}`)
      DataSourceBusinessActions.setMissing(source)
      return
    }
    
    // 从项目目录加载对应的文件
    const file = await globalProjectMediaManager.loadMediaFromProject(
      globalProjectMediaManager.projectId,
      mediaRef.storedPath
    )
    
    // 创建blob URL用于WebAV处理
    const url = URL.createObjectURL(file)
    
    // 设置数据源状态和数据
    DataSourceBusinessActions.completeAcquisition(source, file, url)
    
    console.log(`✅ 本地文件加载成功: ${mediaRef.metadata.originalFileName}`)
  } catch (error) {
    console.error(`加载本地文件失败: ${source.mediaReferenceId}`, error)
    DataSourceBusinessActions.setError(source, `文件加载失败: ${error.message}`)
  }
}

/**
 * 加载远程文件数据源
 * 策略：优先根据引用ID寻找本地缓存，不存在时启动下载流程
 */
async loadRemoteFileSource(source: RemoteFileSourceData): Promise<void> {
  try {
    // 🔍 第一步：检查本地缓存（通过mediaReferenceId）
    if (source.mediaReferenceId) {
      const mediaRef = await globalProjectMediaManager.getMediaReference(source.mediaReferenceId)
      if (mediaRef) {
        console.log(`📦 发现本地缓存: ${mediaRef.metadata.originalFileName}`)
        
        // 从本地加载缓存文件
        const file = await globalProjectMediaManager.loadMediaFromProject(
          globalProjectMediaManager.projectId,
          mediaRef.storedPath
        )
        
        const url = URL.createObjectURL(file)
        DataSourceBusinessActions.completeAcquisition(source, file, url)
        
        console.log(`✅ 远程文件本地缓存加载成功: ${source.remoteUrl}`)
        return
      }
    }
    
    // 🌐 第二步：本地缓存不存在，启动下载流程
    console.log(`🌐 启动远程文件下载: ${source.remoteUrl}`)
    
    // 设置下载状态
    DataSourceStateActions.setAcquiring(source)
    
    // 执行下载（集成RemoteFileManager的下载逻辑）
    const downloadResult = await this.downloadRemoteFile(source)
    
    if (downloadResult.success) {
      // 下载成功后，立即保存到媒体管理器并设置引用ID
      const saveResult = await globalProjectMediaManager.saveMediaToProject(
        downloadResult.file,
        'unknown', // 媒体类型待WebAV解析确定
        null // 下载阶段没有WebAV clip
      )
      
      if (saveResult.success && saveResult.mediaReference) {
        source.mediaReferenceId = saveResult.mediaReference.id
        console.log(`🔗 远程文件下载并保存成功，设置引用ID: ${source.mediaReferenceId}`)
      }
      
      // 设置数据源完成状态
      DataSourceBusinessActions.completeAcquisition(source, downloadResult.file, downloadResult.url)
    } else {
      DataSourceBusinessActions.setError(source, downloadResult.error || '下载失败')
    }
  } catch (error) {
    console.error(`加载远程文件失败: ${source.remoteUrl}`, error)
    DataSourceBusinessActions.setError(source, `下载失败: ${error.message}`)
  }
}
```

**差异化加载策略的核心优势**：

1. **本地文件source (user-selected)**：
   - ✅ 直接通过`mediaReferenceId`从媒体管理器加载已保存的文件
   - ✅ 无需重新读取原始File对象，提升加载性能
   - ✅ 支持文件完整性验证和错误处理

2. **远程文件source (remote)**：
   - ✅ 智能缓存：优先检查本地是否已缓存相同远程文件
   - ✅ 按需下载：本地不存在时才启动下载流程
   - ✅ 自动保存：下载完成后立即保存并设置引用ID
   - ✅ 断网友好：已下载的远程文件可离线使用

3. **统一的错误处理**：
   - ✅ 单个文件加载失败不影响其他文件
   - ✅ 提供详细的错误信息和恢复建议
   - ✅ 支持缺失文件的标记和处理


### 5. 优化后的实施步骤

#### 5.1 阶段0：媒体管理基础设施

**优先级：P0（关键）** - 实现页面级媒体管理器

1. **实现 ProjectMediaManager 类**
   - 页面级实例化，每个项目页面独立管理媒体文件
   - 实现媒体文件保存、加载、验证功能
   - 实现内容哈希计算和去重机制
   - 实现页面内媒体复用功能
   - 实现孤立引用自动清理功能

2. **页面级架构设计**
   - 项目页面加载时实例化对应的媒体管理器
   - 项目保存时只处理项目配置，媒体文件已即时保存
   - 页面跳转时自动清理所有状态


#### 5.2 阶段1：即时保存机制实现

**优先级：P0（关键）** - 解决当前数据安全问题

1. **实现 ProjectMediaManager 的核心功能**
   - 实现 `saveMediaToProject` 方法（页面本地保存）
   - 实现 `generateMediaMetadata` 方法（元数据生成）
   - 实现 `saveMediaMetadata` 方法（元数据保存）
   - 实现 `scanMediaDirectory` 方法（页面本地扫描）
   - 实现文件完整性验证功能

2. **修改 WebAV 处理流程**
   - 在 `UnifiedMediaModule.ts` 的 `startWebAVProcessing` 方法中集成即时保存
   - 解析成功后立即调用页面级媒体管理器保存文件和元数据
   - 在媒体项目中记录存储路径和元数据信息

3. **优化媒体项目与存储引用的关系设计**
   - **核心改进**：将媒体管理器的引用ID从媒体项目ID中分离，放到数据源(source)中管理
   - **设计原则**：`UnifiedMediaItemData.id` 保持业务语义，专注媒体项目的业务逻辑标识
   - **存储引用**：在 `BaseDataSourceData` 中新增 `mediaReferenceId` 字段，专门用于媒体管理器的存储引用
   - **职责分离**：媒体项目专注业务逻辑，存储管理通过数据源的引用ID连接到专门的媒体管理器

   ```typescript
   // UnifiedMediaItemData保持现有设计，id专注业务逻辑
   export interface UnifiedMediaItemData {
     readonly id: string  // 媒体项目的业务ID
     name: string
     source: UnifiedDataSourceData // 数据源包含存储引用信息
     // ... 其他现有字段 ...
   }
   
   // BaseDataSourceData新增媒体管理器引用ID字段
   export interface BaseDataSourceData {
     readonly id: string
     readonly type: string
     // ... 其他现有字段 ...
     
     // 🆕 新增：媒体管理器引用ID，指向ProjectMediaManager中的媒体引用
     mediaReferenceId?: string
   }
   
   // 使用方式：通过数据源的引用ID获取完整存储信息
   const mediaReferenceId = mediaItem.source.mediaReferenceId
   if (mediaReferenceId) {
     const mediaRef = await globalProjectMediaManager.getMediaReference(mediaReferenceId)
     const storagePath = mediaRef?.storedPath
     const metadata = mediaRef?.metadata
   }
   ```

#### 5.3 阶段2：Meta驱动加载机制

**优先级：P1（重要）** - 提升加载性能和用户体验

1. **实现目录扫描功能**
   - 递归扫描媒体目录中的所有 `.meta` 文件
   - 验证对应媒体文件的存在性
   - 构建完整的媒体引用映射

2. **优化项目加载流程**
   - 优先使用扫描到的媒体引用（实际存在的文件）
   - 合并项目配置中的引用和扫描结果
   - 异步重建WebAV对象，不阻塞主加载流程

3. **增强容错机制**
   - 处理媒体文件丢失的情况
   - 处理meta文件损坏的情况
   - 提供文件恢复建议

#### 5.4 阶段3：性能优化和用户体验

**优先级：P2（一般）** - 进一步优化系统性能

1. **性能优化**
   - 实现并发保存队列，避免同时保存冲突
   - 优化大文件的分块处理
   - 实现保存进度反馈

2. **用户体验优化**
   - 提供即时保存状态反馈
   - 实现存储空间监控和警告
   - 提供媒体文件管理界面

3. **错误处理和恢复**
   - 实现自动重试机制
   - 提供手动恢复工具
   - 完善错误日志和诊断信息

#### 5.5 阶段4：测试和验证

**优先级：P1（重要）** - 确保系统质量和稳定性

1. **单元测试**
   - 测试即时保存功能
   - 测试Meta驱动加载功能
   - 测试文件完整性验证
   - 测试错误处理机制

2. **集成测试**
   - 测试完整的素材导入→即时保存→项目保存→项目加载流程
   - 测试不同类型媒体文件的处理
   - 测试大文件和批量文件的处理性能
   - 测试异常情况下的容错能力

3. **用户验收测试**
   - 验证用户体验改进效果
   - 验证数据安全性和完整性
   - 验证系统稳定性和性能表现

### 6. 简化方案的预期效果

通过实施**页面级媒体管理 + 即时保存 + Meta驱动加载**的方案，新架构将具备以下显著改进：

#### 6.1 核心改进

1. **架构简化**：采用页面级媒体管理，通过页面跳转实现天然的项目隔离
2. **按需实例化**：项目页面加载时才创建媒体管理器，节省内存
3. **天然项目隔离**：页面级架构确保项目之间完全隔离，互不影响

#### 6.2 数据安全性提升

1. **即时保存保障**：素材解析成功后立即保存，避免数据丢失
2. **文件完整性验证**：通过SHA-256校验和确保文件完整性
3. **双重保障**：项目配置和Meta文件双重保障

#### 6.3 加载性能优化

1. **Meta驱动加载**：直接从元数据获取信息，显著提升加载速度
2. **数据一致性**：避免配置文件与实际文件状态不一致
3. **异步处理**：WebAV重建不阻塞主加载流程
4. **容错处理**：部分文件丢失不影响其他内容加载

#### 6.4 用户体验改进

1. **即时反馈**：用户可立即看到文件保存状态，增强信任感
2. **内存优化**：及时释放大文件对象，减少内存占用
3. **进度可视化**：提供详细的保存和加载进度反馈

#### 6.5 系统稳定性增强

1. **错误隔离**：单个文件保存失败不影响其他文件和整体流程
2. **自动恢复**：基于Meta文件的自动文件发现和恢复机制
3. **完善的错误处理**：详细的错误分类和处理策略

#### 6.6 开发维护优势

1. **架构清晰**：项目级管理和Meta驱动的设计模式易于理解和维护
2. **扩展性强**：为未来的离线编辑、云同步等功能奠定基础
3. **测试友好**：各个组件职责明确，便于单元测试和集成测试

### 7. 风险评估与应对

#### 7.1 技术风险

1. **文件系统API兼容性**
   - 风险：不同浏览器的文件系统API实现可能存在差异
   - 应对：进行充分的浏览器兼容性测试，提供降级方案

2. **大文件处理性能**
   - 风险：大媒体文件的保存和加载可能影响性能
   - 应对：实现分块处理和进度反馈，优化内存使用

#### 7.2 用户体验风险

1. **保存时间过长**
   - 风险：大量媒体文件可能导致保存时间过长
   - 应对：实现后台保存、进度反馈和取消功能

2. **存储空间不足**
   - 风险：用户设备存储空间不足可能导致保存失败
   - 应对：实现存储空间检查和清理建议

### 8. 方案优势总结

#### 8.1 技术价值

1. **架构先进性**：单一职责原则，符合现代软件设计最佳实践
2. **可维护性**：简化的架构更容易理解和维护
3. **可扩展性**：页面级管理为未来功能扩展提供清晰的边界

#### 8.2 业务价值

1. **开发效率**：页面级架构减少开发和维护成本
2. **系统稳定性**：页面级隔离避免复杂的全局状态管理问题
3. **团队协作**：更清晰的职责划分，便于团队协作开发

### 9. 实施建议

基于页面级架构设计，建议按照以下优先级实施：

1. **P0（关键）**：阶段0和阶段1 - 实现 ProjectMediaManager 和即时保存机制
2. **P1（重要）**：阶段2和阶段4 - Meta驱动加载和测试验证
3. **P2（一般）**：阶段3 - 性能优化和高级功能

这个页面级方案不仅解决了架构理念的矛盾问题，更为项目提供了一个清晰、高效、易维护的媒体管理架构。通过页面级管理的设计，每个项目页面都是完全独立的，这为未来的功能扩展和系统优化提供了良好的基础。