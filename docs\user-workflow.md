# AI视频编辑器 - 用户操作流程指南

## 概述

AI视频编辑器是一个基于Web技术的现代视频编辑应用，采用WebAV SDK作为核心渲染引擎，提供直观的拖拽式视频编辑体验。

## 界面布局

### 主界面结构
```
┌─────────────────────────────────────────────────────────────┐
│                        状态栏                                │
├─────────────┬─────────────────────────┬─────────────────────┤
│             │                         │                     │
│   素材库    │       预览窗口           │     属性面板        │
│             │                         │                     │
│             ├─────────────────────────┤                     │
│             │      播放控制            │                     │
├─────────────┴─────────────────────────┴─────────────────────┤
│                      片段管理工具栏                          │
├─────────────────────────────────────────────────────────────┤
│                        时间轴                               │
│  轨道1  │████████████████████████████████████████████████│
│  轨道2  │████████████████████████████████████████████████│
└─────────────────────────────────────────────────────────────┘
```

### 界面区域说明

1. **状态栏**: 显示应用标题"AI编辑器"
2. **素材库**: 导入和管理视频文件
3. **预览窗口**: 实时预览视频效果
4. **属性面板**: 编辑选中片段的属性
5. **播放控制**: 播放/暂停、停止、速度控制
6. **片段管理工具栏**: 片段操作功能
7. **时间轴**: 多轨道时间线编辑

## 基本操作流程

### 1. 导入视频素材

#### 方法一：点击导入按钮
1. 在素材库区域点击"+"按钮
2. 选择视频文件（支持MP4、WebM、AVI等格式）
3. 等待文件加载完成

#### 方法二：拖拽导入
1. 直接将视频文件拖拽到素材库的拖拽区域
2. 支持同时导入多个文件
3. 文件会自动生成缩略图和时长信息

### 2. 添加视频到时间轴

1. 从素材库中选择要编辑的视频
2. 拖拽视频到时间轴的指定轨道
3. 松开鼠标完成添加
4. 视频片段会显示在时间轴上，包含缩略图和名称

### 3. 视频片段编辑

#### 基本操作
- **移动片段**: 拖拽片段到新位置
- **调整时长**: 拖拽片段边缘调整开始/结束时间
- **跨轨道移动**: 垂直拖拽片段到其他轨道
- **选中片段**: 点击片段进行选择

#### 高级编辑
- **裁剪片段**: 选中片段后点击"裁剪"按钮在当前时间位置分割
- **删除片段**: 右键菜单选择删除或使用删除按钮
- **复制片段**: 右键菜单选择复制
- **自动排列**: 点击"自动排列"消除片段重叠

### 4. 属性调整

选中片段后，在属性面板中可以调整：

#### 基本信息
- **名称**: 修改片段显示名称
- **时长**: 查看片段时长
- **位置**: 查看在时间轴上的位置
- **原始分辨率**: 查看视频原始尺寸

#### 变换属性
- **位置**: X、Y坐标调整
- **缩放**: 等比缩放或分别调整X、Y缩放
- **旋转**: 角度调整（0-360度）
- **透明度**: 0-1之间调整
- **层级**: Z轴顺序调整

#### 播放设置
- **播放速度**: 0.1x - 5x速度调整
- **音量**: 0-100%音量控制

### 5. 预览和播放

#### 播放控制
- **播放/暂停**: 空格键或点击播放按钮
- **停止**: 停止播放并回到开始位置
- **速度控制**: 0.25x - 5x播放速度
- **帧级控制**: 逐帧前进/后退

#### 时间导航
- **时间轴点击**: 直接跳转到指定时间
- **播放头拖拽**: 拖拽红色播放头到目标时间
- **时间显示**: 显示当前时间/总时长

### 6. 视图控制

#### 时间轴缩放
- **鼠标滚轮**: 在时间轴上滚动进行缩放
- **自动缩放**: 根据内容自动调整显示范围

#### 面板调整
- **垂直分割**: 拖拽分割线调整预览区和时间轴高度比例
- **水平分割**: 调整素材库、预览窗口、属性面板宽度
- **最小尺寸**: 各面板都有最小尺寸限制

### 7. 分辨率设置

1. 点击播放控制区域的分辨率按钮
2. 选择预设分辨率（1080p、720p、4K等）
3. 或自定义分辨率尺寸
4. 预览窗口会自动调整显示比例

## 高级功能

### 多轨道编辑
- 支持多个视频轨道同时播放
- 轨道可以独立控制可见性和静音
- 支持轨道重命名和删除
- 自动处理轨道间的层级关系

### 精确编辑
- 帧级精度的时间控制
- 对齐到帧边界的自动吸附
- 精确的像素级位置控制
- 数值输入支持精确调整

### 性能优化
- WebAV引擎提供硬件加速
- 智能缓存和预加载
- 流畅的实时预览
- 大文件支持

## 快捷键

- **空格**: 播放/暂停
- **左/右箭头**: 逐帧移动
- **Delete**: 删除选中片段
- **Ctrl+Z**: 撤销（计划中）
- **Ctrl+Y**: 重做（计划中）

## 注意事项

1. **浏览器兼容性**: 需要Chrome 94+或Edge 94+浏览器
2. **文件格式**: 推荐使用MP4格式以获得最佳兼容性
3. **性能**: 大文件可能需要更长的加载时间
4. **内存**: 同时编辑多个大视频文件时注意内存使用

## 故障排除

### 常见问题
1. **视频无法加载**: 检查文件格式和浏览器兼容性
2. **播放卡顿**: 降低播放速度或关闭其他标签页
3. **拖拽无响应**: 确保文件已完全加载
4. **预览黑屏**: 检查WebCodecs API支持

### 性能优化建议
1. 使用较小的视频文件进行测试
2. 关闭不必要的浏览器扩展
3. 确保有足够的系统内存
4. 使用现代浏览器的最新版本
