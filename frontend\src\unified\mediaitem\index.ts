/**
 * 统一媒体项目模块入口
 * 导出所有数据结构和行为函数
 */

// 导出数据结构和类型
export type {
  MediaStatus,
  MediaType,
  MediaTypeOrUnknown,
  WebAVObjects,
  UnifiedMediaItemData,
  ReadyMediaItem,
  ProcessingMediaItem,
  ErrorMediaItem,
  PendingMediaItem,
  VideoMediaItem,
  ImageMediaItem,
  AudioMediaItem,
  TextMediaItem,
  UnknownMediaItem,
  KnownMediaItem,
  VisualMediaItem,
  AudioCapableMediaItem,
} from './types'

export { createUnifiedMediaItemData } from './types'

// 导出行为函数
export { UnifiedMediaItemQueries, UnifiedMediaItemActions } from './actions'
