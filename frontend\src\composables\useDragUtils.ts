import { useVideoStore } from '../stores/videoStore'
import type {
  TimelineItemDragData,
  MediaItemDragData,
  MediaType,
  TrackType,
  MediaTypeOrUnknown,
} from '../types'
import { alignFramesToFrame } from '../stores/utils/timeUtils'
import { useSnapManager } from './useSnapManager'

/**
 * 拖拽工具函数集合
 * 提供统一的拖拽处理逻辑，避免代码重复
 */
export function useDragUtils() {
  const videoStore = useVideoStore()
  const snapManager = useSnapManager()

  /**
   * 设置时间轴项目拖拽数据
   */
  function setTimelineItemDragData(
    event: DragEvent,
    itemId: string,
    trackId: string,
    startTime: number, // 帧数
    selectedItems: string[],
    dragOffset: { x: number; y: number },
  ) {
    const dragData: TimelineItemDragData = {
      type: 'timeline-item',
      itemId,
      trackId,
      startTime,
      selectedItems,
      dragOffset,
    }

    // 设置拖拽数据
    event.dataTransfer!.setData('application/timeline-item', JSON.stringify(dragData))
    event.dataTransfer!.effectAllowed = 'move'

    // 设置全局拖拽状态（用于dragover事件中访问）
    window.__timelineDragData = dragData

    return dragData
  }

  /**
   * 设置素材库拖拽数据
   */
  function setMediaItemDragData(
    event: DragEvent,
    mediaItemId: string,
    name: string,
    duration: number, // 帧数
    mediaType: 'video' | 'image' | 'audio' | 'text',
  ) {
    const dragData: MediaItemDragData = {
      type: 'media-item',
      mediaItemId,
      name,
      duration,
      mediaType,
    }

    // 设置拖拽数据
    event.dataTransfer!.setData('application/media-item', JSON.stringify(dragData))
    event.dataTransfer!.effectAllowed = 'copy'

    // 设置全局拖拽状态
    window.__mediaDragData = dragData

    return dragData
  }

  /**
   * 获取当前时间轴项目拖拽数据
   */
  function getCurrentTimelineItemDragData(): TimelineItemDragData | null {
    return window.__timelineDragData || null
  }

  /**
   * 获取当前素材库拖拽数据
   */
  function getCurrentMediaItemDragData(): MediaItemDragData | null {
    return window.__mediaDragData || null
  }

  /**
   * 清理拖拽数据
   */
  function clearDragData() {
    window.__timelineDragData = null
    window.__mediaDragData = null
  }

  /**
   * 确保项目被选中（拖拽开始时调用）
   */
  function ensureItemSelected(itemId: string) {
    if (!videoStore.selectedTimelineItemIds.has(itemId)) {
      videoStore.selectTimelineItem(itemId)
    }
  }

  /**
   * 根据媒体类型计算clip高度
   */
  function getClipHeightByMediaType(mediaType: MediaTypeOrUnknown): number {
    // 统一所有类型的clip高度为50px
    return 50 // 所有clip统一高度50px，轨道高度60px，上下各留5px间距
  }

  /**
   * 获取被拖拽项目的实际高度
   */
  function getDraggedItemHeight(): number {
    // 检查是否是时间轴项目拖拽
    const timelineDragData = getCurrentTimelineItemDragData()
    if (timelineDragData) {
      const draggedItem = videoStore.getTimelineItem(timelineDragData.itemId)
      if (draggedItem) {
        return getClipHeightByMediaType(draggedItem.mediaType)
      }
    }

    // 检查是否是素材库拖拽
    const mediaDragData = getCurrentMediaItemDragData()
    if (mediaDragData) {
      return getClipHeightByMediaType(mediaDragData.mediaType)
    }

    // 默认高度
    return 50
  }

  /**
   * 创建拖拽预览的基础数据
   */
  function createDragPreviewData(
    name: string,
    duration: number, // 帧数
    startTime: number, // 帧数
    trackId: string,
    isConflict: boolean = false,
    isMultiple: boolean = false,
    count?: number,
    mediaType?: MediaType,
  ) {
    // 计算预览高度
    let height: number
    if (mediaType) {
      height = getClipHeightByMediaType(mediaType)
    } else {
      height = getDraggedItemHeight()
    }

    return {
      name,
      duration,
      startTime,
      trackId,
      isConflict,
      isMultiple,
      count,
      height,
      mediaType,
    }
  }

  /**
   * 检查媒体类型与轨道类型的兼容性
   */
  function isMediaCompatibleWithTrack(
    mediaType: MediaTypeOrUnknown,
    trackType: TrackType,
  ): boolean {
    // 视频轨道支持视频和图片素材
    if (mediaType === 'unknown') return true
    if (trackType === 'video') {
      return mediaType === 'video' || mediaType === 'image'
    }

    // 音频轨道支持音频素材
    if (trackType === 'audio') {
      return mediaType === 'audio'
    }

    // 文本轨道支持文本素材
    if (trackType === 'text') {
      return mediaType === 'text'
    }

    return false
  }

  /**
   * 根据媒体类型寻找最近的兼容轨道
   */
  function findNearestCompatibleTrack(
    mouseY: number,
    mediaType: MediaTypeOrUnknown,
  ): string | null {
    const tracks = videoStore.tracks
    if (tracks.length === 0) return null

    // 获取所有兼容的轨道及其位置信息
    const compatibleTracks: Array<{ id: string; distance: number; element: HTMLElement }> = []

    tracks.forEach((track) => {
      if (isMediaCompatibleWithTrack(mediaType, track.type)) {
        const trackElement = document.querySelector(`[data-track-id="${track.id}"]`) as HTMLElement
        if (trackElement) {
          const rect = trackElement.getBoundingClientRect()
          const trackCenterY = rect.top + rect.height / 2
          const distance = Math.abs(mouseY - trackCenterY)
          compatibleTracks.push({
            id: track.id,
            distance,
            element: trackElement,
          })
        }
      }
    })

    if (compatibleTracks.length === 0) return null

    // 返回距离最近的兼容轨道
    compatibleTracks.sort((a, b) => a.distance - b.distance)
    return compatibleTracks[0].id
  }

  /**
   * 计算拖拽目标位置（支持轨道类型兼容性检查和吸附）
   */
  function calculateDropPosition(
    event: DragEvent,
    timelineWidth: number,
    dragOffset?: { x: number; y: number },
    enableSnapping: boolean = true,
  ) {
    const targetElement = event.target as HTMLElement
    const trackContent = targetElement.closest('.track-content')

    if (!trackContent) {
      return null
    }

    const rect = trackContent.getBoundingClientRect()
    const mouseX = event.clientX - rect.left
    let targetTrackId = trackContent.getAttribute('data-track-id') || videoStore.tracks[0]?.id || ''

    // 获取拖拽的媒体类型和排除的片段ID
    let draggedMediaType: MediaTypeOrUnknown | null = null
    let excludeClipIds: string[] = []

    // 检查是否是时间轴项目拖拽
    const timelineDragData = getCurrentTimelineItemDragData()
    if (timelineDragData) {
      const draggedItem = videoStore.getTimelineItem(timelineDragData.itemId)
      if (draggedItem) {
        draggedMediaType = draggedItem.mediaType
      }
      // 排除当前拖拽的所有选中片段
      excludeClipIds = timelineDragData.selectedItems
    } else {
      // 检查是否是素材库拖拽
      const mediaDragData = getCurrentMediaItemDragData()
      if (mediaDragData) {
        draggedMediaType = mediaDragData.mediaType
      }
      // 素材库拖拽不需要排除任何片段
      excludeClipIds = []
    }

    // 如果能获取到媒体类型，检查轨道兼容性
    if (draggedMediaType) {
      const currentTrack = videoStore.tracks.find((t) => t.id === targetTrackId)
      if (currentTrack && !isMediaCompatibleWithTrack(draggedMediaType, currentTrack.type)) {
        // 当前轨道不兼容，寻找最近的兼容轨道
        const nearestCompatibleTrackId = findNearestCompatibleTrack(event.clientY, draggedMediaType)
        if (nearestCompatibleTrackId) {
          targetTrackId = nearestCompatibleTrackId
          // 更新trackContent为新的目标轨道
          const newTrackContent = document.querySelector(
            `[data-track-id="${targetTrackId}"]`,
          ) as HTMLElement
          if (newTrackContent) {
            const newRect = newTrackContent.getBoundingClientRect()
            // 重新计算mouseX相对于新轨道的位置
            const mouseXRelativeToNewTrack = event.clientX - newRect.left
            // 使用新的相对位置计算时间（支持吸附）
            const dropResult = calculateDropFrames(
              mouseXRelativeToNewTrack,
              timelineWidth,
              dragOffset,
              enableSnapping,
              excludeClipIds,
            )
            return {
              dropTime: dropResult.frame,
              targetTrackId,
              trackContent: newTrackContent,
              snapResult: dropResult.snapResult,
            }
          }
        }
      }
    }

    // 使用原始逻辑计算时间（支持吸附）
    const dropResult = calculateDropFrames(
      mouseX,
      timelineWidth,
      dragOffset,
      enableSnapping,
      excludeClipIds,
    )

    return {
      dropTime: dropResult.frame,
      targetTrackId,
      trackContent,
      snapResult: dropResult.snapResult,
    }
  }

  /**
   * 计算拖拽帧数的辅助函数（支持吸附）
   */
  function calculateDropFrames(
    mouseX: number,
    timelineWidth: number,
    dragOffset?: { x: number; y: number },
    enableSnapping: boolean = true,
    excludeClipIds?: string[],
  ): { frame: number; snapResult?: any } {
    // 使用帧数进行精确计算
    let dropFrames: number
    if (dragOffset) {
      // 考虑拖拽偏移量，计算clip的实际开始位置
      const clipStartX = mouseX - dragOffset.x
      dropFrames = videoStore.pixelToFrame(clipStartX, timelineWidth)
    } else {
      // 直接使用鼠标位置
      dropFrames = videoStore.pixelToFrame(mouseX, timelineWidth)
    }

    // 确保拖拽帧数不会小于0
    dropFrames = Math.max(0, dropFrames)

    // 对齐到帧边界
    dropFrames = alignFramesToFrame(dropFrames)

    // 应用吸附计算（如果启用）
    let snapResult
    if (enableSnapping) {
      snapResult = snapManager.calculateClipDragSnap(
        dropFrames,
        timelineWidth,
        excludeClipIds || [],
        {
          temporaryDisabled: false,
        },
      )

      // 如果发生了吸附，使用吸附后的帧数
      if (snapResult.snapped) {
        dropFrames = snapResult.frame
      }
    }

    return { frame: dropFrames, snapResult }
  }

  /**
   * 检查拖拽数据类型
   */
  function getDragDataType(event: DragEvent): 'timeline-item' | 'media-item' | 'files' | 'unknown' {
    const types = event.dataTransfer?.types || []

    if (types.includes('application/timeline-item')) {
      return 'timeline-item'
    } else if (types.includes('application/media-item')) {
      return 'media-item'
    } else if (types.includes('Files')) {
      return 'files'
    } else {
      return 'unknown'
    }
  }

  /**
   * 通过数据属性查询DOM元素的实用函数
   */
  function getTimelineItemElement(timelineItemId: string): HTMLElement | null {
    return document.querySelector(`[data-timeline-item-id="${timelineItemId}"]`)
  }

  function getMediaItemElement(mediaItemId: string): HTMLElement | null {
    return document.querySelector(`[data-media-item-id="${mediaItemId}"]`)
  }

  function getTrackElement(trackId: string): HTMLElement | null {
    return document.querySelector(`[data-track-id="${trackId}"]`)
  }

  /**
   * 获取元素的实际尺寸信息
   */
  function getElementDimensions(element: HTMLElement | null): { width: number; height: number } {
    if (!element) {
      return { width: 100, height: 60 } // 默认尺寸
    }
    return {
      width: element.offsetWidth,
      height: element.offsetHeight,
    }
  }

  return {
    // 拖拽数据管理
    setTimelineItemDragData,
    setMediaItemDragData,
    getCurrentTimelineItemDragData,
    getCurrentMediaItemDragData,
    clearDragData,

    // 选择管理
    ensureItemSelected,

    // 预览数据创建
    createDragPreviewData,
    getClipHeightByMediaType,
    getDraggedItemHeight,

    // 位置计算
    calculateDropPosition,
    findNearestCompatibleTrack,
    isMediaCompatibleWithTrack,

    // 类型检查
    getDragDataType,

    // DOM查询工具
    getTimelineItemElement,
    getMediaItemElement,
    getTrackElement,
    getElementDimensions,
  }
}
