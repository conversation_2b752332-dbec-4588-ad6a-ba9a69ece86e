{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@imengyu/vue3-context-menu": "^1.5.2", "@webav/av-canvas": "^1.1.17", "@webav/av-cliper": "^1.1.17", "lodash": "^4.17.21", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@tsconfig/node22": "^22.0.2", "@types/lodash": "^4.17.20", "@types/node": "^24.2.1", "@types/wicg-file-system-access": "^2023.10.6", "@vitejs/plugin-vue": "^6.0.1", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.33.0", "eslint-plugin-vue": "~10.4.0", "jiti": "^2.5.1", "npm-run-all2": "^8.0.4", "prettier": "3.6.2", "typescript": "~5.9.2", "vite": "^7.1.1", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.5"}}